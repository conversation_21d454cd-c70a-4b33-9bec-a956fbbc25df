package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	fmt.Println("Testing Gemini Native API Endpoints...")

	// 测试配置
	baseURL := "http://localhost:8080"
	apiKey := "AIzaSyCOJVcNJ64WdOfnRfIVyXCImzID0ox9b_c" // 替换为实际的API密钥
	model := "gemini-2.5-pro"

	// 测试1: 检查 /v1/beta 端点是否存在
	fmt.Println("\n=== Test 1: Check /v1/beta endpoint ===")
	testEndpointExists(baseURL + "/v1/beta")

	// 测试2: 测试 Gemini 原生模型列表
	fmt.Println("\n=== Test 2: Gemini Native Models ===")
	testGeminiNativeModels(baseURL, apiKey)

	// 测试3: 测试 Gemini 原生聊天完成
	fmt.Println("\n=== Test 3: Gemini Native Chat Completion ===")
	testGeminiNativeChat(baseURL, apiKey, model)

	// 测试4: 测试 Gemini 原生流式聊天完成
	fmt.Println("\n=== Test 4: Gemini Native Stream Chat ===")
	testGeminiNativeStreamChat(baseURL, apiKey, model)

	fmt.Println("\n🎉 All tests completed!")
}

func testEndpointExists(url string) {
	resp, err := http.Get(url)
	if err != nil {
		fmt.Printf("❌ Error accessing %s: %v\n", url, err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		fmt.Printf("❌ Endpoint %s returns 404 Not Found\n", url)
	} else {
		fmt.Printf("✅ Endpoint %s is accessible (status: %d)\n", url, resp.StatusCode)
	}
}

func testGeminiNativeModels(baseURL, apiKey string) {
	url := baseURL + "/v1/beta/models"
	
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Printf("❌ Error creating request: %v\n", err)
		return
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ Error making request: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Error reading response: %v\n", err)
		return
	}

	fmt.Printf("Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(body))

	if resp.StatusCode == 200 {
		fmt.Println("✅ Gemini native models endpoint works!")
	} else {
		fmt.Printf("❌ Gemini native models endpoint failed with status %d\n", resp.StatusCode)
	}
}

func testGeminiNativeChat(baseURL, apiKey, model string) {
	url := fmt.Sprintf("%s/v1/beta/models/%s/generateContent", baseURL, model)
	
	requestBody := map[string]interface{}{
		"contents": []map[string]interface{}{
			{
				"role": "user",
				"parts": []map[string]interface{}{
					{
						"text": "Hello! Please respond with a simple greeting.",
					},
				},
			},
		},
		"generationConfig": map[string]interface{}{
			"temperature":     0.7,
			"maxOutputTokens": 100,
			"topP":            0.9,
		},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Printf("❌ Error marshaling request: %v\n", err)
		return
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Error creating request: %v\n", err)
		return
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ Error making request: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Error reading response: %v\n", err)
		return
	}

	fmt.Printf("Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(body))

	if resp.StatusCode == 200 {
		fmt.Println("✅ Gemini native chat completion works!")
		
		// 尝试解析响应以验证格式
		var response map[string]interface{}
		if err := json.Unmarshal(body, &response); err == nil {
			if _, ok := response["candidates"]; ok {
				fmt.Println("✅ Response has Gemini native format (candidates field found)")
			} else {
				fmt.Println("⚠️  Response doesn't have expected Gemini native format")
			}
		}
	} else {
		fmt.Printf("❌ Gemini native chat completion failed with status %d\n", resp.StatusCode)
	}
}

func testGeminiNativeStreamChat(baseURL, apiKey, model string) {
	url := fmt.Sprintf("%s/v1/beta/models/%s/streamGenerateContent", baseURL, model)
	
	requestBody := map[string]interface{}{
		"contents": []map[string]interface{}{
			{
				"role": "user",
				"parts": []map[string]interface{}{
					{
						"text": "Count from 1 to 5, one number per response.",
					},
				},
			},
		},
		"generationConfig": map[string]interface{}{
			"temperature":     0.1,
			"maxOutputTokens": 50,
		},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Printf("❌ Error marshaling request: %v\n", err)
		return
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Error creating request: %v\n", err)
		return
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ Error making request: %v\n", err)
		return
	}
	defer resp.Body.Close()

	fmt.Printf("Status: %d\n", resp.StatusCode)

	if resp.StatusCode == 200 {
		fmt.Println("✅ Gemini native stream chat endpoint is accessible!")
		fmt.Println("Stream response preview (first 500 chars):")
		
		// 读取前500个字符作为预览
		buffer := make([]byte, 500)
		n, err := resp.Body.Read(buffer)
		if err != nil && err != io.EOF {
			fmt.Printf("❌ Error reading stream: %v\n", err)
			return
		}
		
		fmt.Printf("Stream data: %s\n", string(buffer[:n]))
		
		if bytes.Contains(buffer[:n], []byte("data:")) {
			fmt.Println("✅ Stream response contains SSE format!")
		}
	} else {
		body, _ := io.ReadAll(resp.Body)
		fmt.Printf("❌ Gemini native stream chat failed with status %d\n", resp.StatusCode)
		fmt.Printf("Error response: %s\n", string(body))
	}
}
