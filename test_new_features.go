package main

import (
	"fmt"
	"log"
	"time"
	"turnsapi/internal"
	"turnsapi/internal/database"
)

func main() {
	// 测试新功能
	fmt.Println("Testing new features: RPM Limit and Use Native Response")

	// 创建测试分组
	testGroup := &database.UserGroup{
		Name:              "test-group",
		ProviderType:      "gemini",
		BaseURL:           "https://generativelanguage.googleapis.com/v1beta",
		Enabled:           true,
		Timeout:           30 * time.Second,
		MaxRetries:        3,
		RotationStrategy:  "round_robin",
		APIKeys:           []string{"test-key-1", "test-key-2"},
		UseNativeResponse: true,  // 测试原生响应
		RPMLimit:          60,    // 测试RPM限制
	}

	fmt.Printf("Test Group Configuration:\n")
	fmt.Printf("  Name: %s\n", testGroup.Name)
	fmt.Printf("  Provider Type: %s\n", testGroup.ProviderType)
	fmt.Printf("  Use Native Response: %v\n", testGroup.UseNativeResponse)
	fmt.Printf("  RPM Limit: %d\n", testGroup.RPMLimit)
	fmt.Printf("  API Keys: %v\n", testGroup.APIKeys)

	// 测试数据库操作
	fmt.Println("\nTesting database operations...")

	// 初始化数据库
	groupsDB, err := database.NewGroupsDB("test_groups.db")
	if err != nil {
		log.Fatalf("Failed to create database: %v", err)
	}
	defer groupsDB.Close()

	// 保存测试分组
	err = groupsDB.SaveGroup("test-group", testGroup)
	if err != nil {
		log.Fatalf("Failed to save group: %v", err)
	}
	fmt.Println("✅ Group saved successfully")

	// 加载测试分组
	loadedGroup, err := groupsDB.LoadGroup("test-group")
	if err != nil {
		log.Fatalf("Failed to load group: %v", err)
	}

	fmt.Printf("Loaded Group Configuration:\n")
	fmt.Printf("  Name: %s\n", loadedGroup.Name)
	fmt.Printf("  Provider Type: %s\n", loadedGroup.ProviderType)
	fmt.Printf("  Use Native Response: %v\n", loadedGroup.UseNativeResponse)
	fmt.Printf("  RPM Limit: %d\n", loadedGroup.RPMLimit)
	fmt.Printf("  API Keys: %v\n", loadedGroup.APIKeys)

	// 验证字段是否正确保存和加载
	if loadedGroup.UseNativeResponse != testGroup.UseNativeResponse {
		log.Fatalf("UseNativeResponse mismatch: expected %v, got %v", testGroup.UseNativeResponse, loadedGroup.UseNativeResponse)
	}

	if loadedGroup.RPMLimit != testGroup.RPMLimit {
		log.Fatalf("RPMLimit mismatch: expected %d, got %d", testGroup.RPMLimit, loadedGroup.RPMLimit)
	}

	fmt.Println("✅ All fields saved and loaded correctly")

	// 测试GetGroupsWithMetadata
	fmt.Println("\nTesting GetGroupsWithMetadata...")
	groupsWithMetadata, err := groupsDB.GetGroupsWithMetadata()
	if err != nil {
		log.Fatalf("Failed to get groups with metadata: %v", err)
	}

	if groupData, exists := groupsWithMetadata["test-group"]; exists {
		fmt.Printf("Group metadata:\n")
		fmt.Printf("  use_native_response: %v\n", groupData["use_native_response"])
		fmt.Printf("  rpm_limit: %v\n", groupData["rpm_limit"])
		fmt.Printf("  created_at: %v\n", groupData["created_at"])
		fmt.Printf("  updated_at: %v\n", groupData["updated_at"])
	} else {
		log.Fatalf("Test group not found in metadata")
	}

	fmt.Println("✅ GetGroupsWithMetadata works correctly")

	// 测试internal.UserGroup结构体
	fmt.Println("\nTesting internal.UserGroup compatibility...")
	internalGroup := &internal.UserGroup{
		Name:              "internal-test-group",
		ProviderType:      "anthropic",
		BaseURL:           "https://api.anthropic.com/v1",
		Enabled:           true,
		Timeout:           45 * time.Second,
		MaxRetries:        2,
		RotationStrategy:  "random",
		APIKeys:           []string{"internal-key-1"},
		UseNativeResponse: false,
		RPMLimit:          120,
	}

	fmt.Printf("Internal Group Configuration:\n")
	fmt.Printf("  Name: %s\n", internalGroup.Name)
	fmt.Printf("  Use Native Response: %v\n", internalGroup.UseNativeResponse)
	fmt.Printf("  RPM Limit: %d\n", internalGroup.RPMLimit)

	fmt.Println("\n🎉 All tests passed! New features are working correctly.")
	fmt.Println("\nFeatures implemented:")
	fmt.Println("✅ RPM Limit field in database and UserGroup struct")
	fmt.Println("✅ Use Native Response field in database and UserGroup struct")
	fmt.Println("✅ Database migration for new fields")
	fmt.Println("✅ Save/Load operations with new fields")
	fmt.Println("✅ GetGroupsWithMetadata includes new fields")
	fmt.Println("✅ Native response conversion logic in proxy")
}
