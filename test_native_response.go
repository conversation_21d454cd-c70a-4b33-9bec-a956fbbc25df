package main

import (
	"fmt"
	"turnsapi/internal/providers"
)

func main() {
	fmt.Println("Testing Native Response Conversion...")

	// 创建一个模拟的标准响应
	standardResponse := &providers.ChatCompletionResponse{
		ID:      "chatcmpl-test123",
		Object:  "chat.completion",
		Created: **********,
		Model:   "gemini-2.5-flash",
		Choices: []struct {
			Index   int `json:"index"`
			Message struct {
				Role    string `json:"role"`
				Content string `json:"content"`
			} `json:"message"`
			FinishReason string `json:"finish_reason"`
		}{
			{
				Index: 0,
				Message: struct {
					Role    string `json:"role"`
					Content string `json:"content"`
				}{
					Role:    "assistant",
					Content: "Hello! This is a test response from <PERSON>.",
				},
				FinishReason: "stop",
			},
		},
		Usage: struct {
			PromptTokens     int `json:"prompt_tokens"`
			CompletionTokens int `json:"completion_tokens"`
			TotalTokens      int `json:"total_tokens"`
		}{
			PromptTokens:     10,
			CompletionTokens: 15,
			TotalTokens:      25,
		},
	}

	// 创建一个模拟的代理
	mockProxy := &MockProxy{}

	fmt.Println("\n=== Testing Gemini Native Response Conversion ===")
	
	// 测试Gemini原生响应转换
	nativeResponse, err := mockProxy.convertToGeminiNativeResponse(standardResponse)
	if err != nil {
		fmt.Printf("❌ Error converting to Gemini native response: %v\n", err)
	} else {
		fmt.Println("✅ Gemini native response conversion successful")
		fmt.Printf("Native Response Structure: %+v\n", nativeResponse)
	}

	fmt.Println("\n=== Testing Anthropic Native Response Conversion ===")
	
	// 测试Anthropic原生响应转换
	anthropicResponse, err := mockProxy.convertToAnthropicNativeResponse(standardResponse)
	if err != nil {
		fmt.Printf("❌ Error converting to Anthropic native response: %v\n", err)
	} else {
		fmt.Println("✅ Anthropic native response conversion successful")
		fmt.Printf("Native Response Structure: %+v\n", anthropicResponse)
	}

	fmt.Println("\n=== Testing OpenAI Native Response (Should Return Original) ===")
	
	// 创建一个模拟的OpenAI提供商
	mockOpenAIProvider := &MockProvider{providerType: "openai"}
	
	// 测试OpenAI原生响应（应该返回原始响应）
	openaiResponse, err := mockProxy.getNativeResponse(mockOpenAIProvider, standardResponse)
	if err != nil {
		fmt.Printf("❌ Error getting OpenAI native response: %v\n", err)
	} else {
		fmt.Println("✅ OpenAI native response handling successful (returns original)")
		// 检查是否返回了原始响应
		if openaiResponse == standardResponse {
			fmt.Println("✅ Correctly returned original response for OpenAI")
		} else {
			fmt.Println("⚠️  OpenAI response was modified (unexpected)")
		}
	}

	fmt.Println("\n🎉 All native response conversion tests completed!")
}

// MockProvider 模拟提供商
type MockProvider struct {
	providerType string
}

func (m *MockProvider) GetProviderType() string {
	return m.providerType
}

// MockProxy 模拟代理，包含转换方法
type MockProxy struct{}

// convertToGeminiNativeResponse 转换为Gemini原生响应格式
func (p *MockProxy) convertToGeminiNativeResponse(response *providers.ChatCompletionResponse) (interface{}, error) {
	// 构造Gemini原生响应格式
	nativeResponse := map[string]interface{}{
		"candidates": []map[string]interface{}{
			{
				"content": map[string]interface{}{
					"parts": []map[string]interface{}{
						{
							"text": response.Choices[0].Message.Content,
						},
					},
					"role": "model",
				},
				"finishReason": response.Choices[0].FinishReason,
				"index":       response.Choices[0].Index,
			},
		},
		"usageMetadata": map[string]interface{}{
			"promptTokenCount":     response.Usage.PromptTokens,
			"candidatesTokenCount": response.Usage.CompletionTokens,
			"totalTokenCount":      response.Usage.TotalTokens,
		},
	}
	
	return nativeResponse, nil
}

// convertToAnthropicNativeResponse 转换为Anthropic原生响应格式
func (p *MockProxy) convertToAnthropicNativeResponse(response *providers.ChatCompletionResponse) (interface{}, error) {
	// 构造Anthropic原生响应格式
	nativeResponse := map[string]interface{}{
		"id":      response.ID,
		"type":    "message",
		"role":    "assistant",
		"content": []map[string]interface{}{
			{
				"type": "text",
				"text": response.Choices[0].Message.Content,
			},
		},
		"model":       response.Model,
		"stop_reason": response.Choices[0].FinishReason,
		"usage": map[string]interface{}{
			"input_tokens":  response.Usage.PromptTokens,
			"output_tokens": response.Usage.CompletionTokens,
		},
	}
	
	return nativeResponse, nil
}

// getNativeResponse 获取提供商的原生响应格式
func (p *MockProxy) getNativeResponse(provider *MockProvider, standardResponse *providers.ChatCompletionResponse) (interface{}, error) {
	// 根据提供商类型返回相应的原生格式
	switch provider.GetProviderType() {
	case "gemini":
		return p.convertToGeminiNativeResponse(standardResponse)
	case "anthropic":
		return p.convertToAnthropicNativeResponse(standardResponse)
	case "openai", "azure_openai":
		// OpenAI格式本身就是标准格式，直接返回
		return standardResponse, nil
	default:
		// 对于未知提供商，返回标准格式
		return standardResponse, nil
	}
}
