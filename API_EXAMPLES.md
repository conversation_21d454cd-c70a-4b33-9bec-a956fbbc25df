# TurnsAPI 使用示例

本文档提供了 TurnsAPI v2.2.0 新功能的详细使用示例。

## 🌐 原生接口响应功能

### 配置原生响应

在分组配置中启用原生响应：

```yaml
user_groups:
  google_gemini:
    name: "Google Gemini"
    provider_type: "gemini"
    base_url: "https://generativelanguage.googleapis.com/v1beta"
    enabled: true
    api_keys:
      - "your-gemini-api-key"
    models:
      - "gemini-2.5-pro"
      - "gemini-2.5-flash"
    # 启用原生接口响应
    use_native_response: true
```

### 使用 Gemini 原生 API

#### 1. 非流式请求

```bash
curl -X POST http://localhost:8080/v1/beta/models/gemini-2.5-pro/generateContent \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-access-token" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "解释一下量子计算的基本原理"
          }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 0.7,
      "maxOutputTokens": 1000,
      "topP": 0.9,
      "stopSequences": ["END"]
    }
  }'
```

#### 2. 流式请求

```bash
curl -X POST http://localhost:8080/v1/beta/models/gemini-2.5-pro/streamGenerateContent \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-access-token" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "写一个关于人工智能的短故事"
          }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 0.8,
      "maxOutputTokens": 2000
    }
  }'
```

#### 3. 原生响应格式

```json
{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "量子计算是一种基于量子力学原理的计算方式..."
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 15,
    "candidatesTokenCount": 120,
    "totalTokenCount": 135
  }
}
```

## ⚡ RPM 限制功能

### 配置 RPM 限制

```yaml
user_groups:
  openai_limited:
    name: "OpenAI Limited"
    provider_type: "openai"
    base_url: "https://api.openai.com/v1"
    enabled: true
    api_keys:
      - "sk-your-openai-key"
    models:
      - "gpt-4"
      - "gpt-3.5-turbo"
    # 每分钟最多 60 次请求
    rpm_limit: 60
```

### RPM 限制响应

当超出限制时，API 会返回 429 错误：

```json
{
  "error": {
    "message": "Rate limit exceeded for group 'openai_limited'. Limit: 60 requests per minute",
    "type": "rate_limit_exceeded",
    "code": "rpm_limit_exceeded"
  }
}
```

## 🔄 混合使用示例

### 同时配置多个功能

```yaml
user_groups:
  gemini_premium:
    name: "Gemini Premium"
    provider_type: "gemini"
    base_url: "https://generativelanguage.googleapis.com/v1beta"
    enabled: true
    api_keys:
      - "your-gemini-api-key-1"
      - "your-gemini-api-key-2"
    models:
      - "gemini-2.5-pro"
      - "gemini-2.5-flash"
    rotation_strategy: "round_robin"
    # 启用原生响应
    use_native_response: true
    # RPM 限制
    rpm_limit: 120
    # 模型映射
    model_mappings:
      "gemini-pro": "gemini-2.5-pro"
      "gemini-flash": "gemini-2.5-flash"
    # 参数覆盖
    request_params:
      temperature: 0.7
      maxOutputTokens: 1500
```

## 📊 监控和管理

### 查看 RPM 限制状态

```bash
curl -X GET http://localhost:8080/admin/rpm-stats \
  -H "Authorization: Bearer your-access-token"
```

响应示例：
```json
{
  "rpm_stats": {
    "openai_limited": {
      "limit": 60,
      "current": 45,
      "remaining": 15,
      "reset_time": "2024-01-01T12:01:00Z"
    },
    "gemini_premium": {
      "limit": 120,
      "current": 89,
      "remaining": 31,
      "reset_time": "2024-01-01T12:01:00Z"
    }
  }
}
```

## 🛠️ 开发集成示例

### Python 客户端示例

```python
import requests
import json

# 使用 OpenAI 兼容格式
def chat_openai_format():
    url = "http://localhost:8080/v1/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer your-access-token"
    }
    data = {
        "model": "gpt-4",
        "messages": [
            {"role": "user", "content": "Hello!"}
        ],
        "stream": False
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()

# 使用 Gemini 原生格式
def chat_gemini_native():
    url = "http://localhost:8080/v1/beta/models/gemini-2.5-pro/generateContent"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer your-access-token"
    }
    data = {
        "contents": [
            {
                "role": "user",
                "parts": [{"text": "Hello!"}]
            }
        ],
        "generationConfig": {
            "temperature": 0.7,
            "maxOutputTokens": 1000
        }
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # OpenAI 格式
    openai_result = chat_openai_format()
    print("OpenAI Format:", json.dumps(openai_result, indent=2))
    
    # Gemini 原生格式
    gemini_result = chat_gemini_native()
    print("Gemini Native:", json.dumps(gemini_result, indent=2))
```

### JavaScript 客户端示例

```javascript
// 使用 Gemini 原生 API
async function chatGeminiNative() {
    const response = await fetch('http://localhost:8080/v1/beta/models/gemini-2.5-pro/generateContent', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer your-access-token'
        },
        body: JSON.stringify({
            contents: [
                {
                    role: 'user',
                    parts: [{ text: 'Hello from JavaScript!' }]
                }
            ],
            generationConfig: {
                temperature: 0.7,
                maxOutputTokens: 1000
            }
        })
    });
    
    return await response.json();
}

// 流式请求示例
async function streamGeminiNative() {
    const response = await fetch('http://localhost:8080/v1/beta/models/gemini-2.5-pro/streamGenerateContent', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer your-access-token'
        },
        body: JSON.stringify({
            contents: [
                {
                    role: 'user',
                    parts: [{ text: 'Tell me a story' }]
                }
            ]
        })
    });
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
            if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                    console.log('Stream finished');
                    return;
                }
                
                try {
                    const parsed = JSON.parse(data);
                    console.log('Received:', parsed);
                } catch (e) {
                    // 忽略解析错误
                }
            }
        }
    }
}
```

## 🔧 故障排除

### 常见问题

1. **RPM 限制错误**
   - 检查分组的 `rpm_limit` 配置
   - 等待限制重置（每分钟重置一次）
   - 考虑增加 API 密钥数量或调整限制值

2. **原生响应格式错误**
   - 确保分组配置中 `use_native_response: true`
   - 检查请求格式是否符合提供商原生API规范
   - 验证模型名称是否正确

3. **端点不可用**
   - 确认服务器已启动并监听正确端口
   - 检查防火墙和网络配置
   - 验证认证令牌是否有效

### 调试技巧

1. **启用调试日志**
   ```yaml
   logging:
     level: "debug"
   ```

2. **检查服务状态**
   ```bash
   curl http://localhost:8080/health
   ```

3. **查看实时日志**
   ```bash
   tail -f logs/turnsapi.log
   ```
